{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:20:03.204",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-3",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808092003C4D4D16A433F194153BA"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:20:03 GMT], expires=[Fri, 08 Aug 2025 01:20:03 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=31, origin; dur=12, inner; dur=3], tt_stable=[0], x-akamai-request-id=[1dc67391.1d33e15], x-cache=[TCP_MISS from a23-32-20-53.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a23-221-208-240.deploy.akamaitechnologies.com (AkamaiGHost/22.2.0-44748e1838d946f91603bfea222ba018) (-)], x-origin-response-time=[12,**************], x-parent-response-time=[43,***********], x-tt-logid=[20250808092003C4D4D16A433F194153BA], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7ee5a0b437467c2efea3c7a9a102eb7b9fa0e235b4625c51f87dc4bac5956714274a4818cb15856a3c2e69a9a9b15b4b92b074443428142352e47f4e7e16554daccee28cd7aec169f710e3687d9237f92bc93d69d4a749a15e9ac555333f978622], x-tt-trace-id=[00-250808092003C4D4D16A433F194153BA-2AA1E1636550FFC8-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}" }
                    
tiktokshop.open.sdk_java.invoke.ApiException: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808092003C4D4D16A433F194153BA"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:20:03 GMT], expires=[Fri, 08 Aug 2025 01:20:03 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=31, origin; dur=12, inner; dur=3], tt_stable=[0], x-akamai-request-id=[1dc67391.1d33e15], x-cache=[TCP_MISS from a23-32-20-53.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a23-221-208-240.deploy.akamaitechnologies.com (AkamaiGHost/22.2.0-44748e1838d946f91603bfea222ba018) (-)], x-origin-response-time=[12,**************], x-parent-response-time=[43,***********], x-tt-logid=[20250808092003C4D4D16A433F194153BA], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7ee5a0b437467c2efea3c7a9a102eb7b9fa0e235b4625c51f87dc4bac5956714274a4818cb15856a3c2e69a9a9b15b4b92b074443428142352e47f4e7e16554daccee28cd7aec169f710e3687d9237f92bc93d69d4a749a15e9ac555333f978622], x-tt-trace-id=[00-250808092003C4D4D16A433F194153BA-2AA1E1636550FFC8-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}
	at tiktokshop.open.sdk_java.invoke.ApiClient.handleResponse(ApiClient.java:1198)
	at tiktokshop.open.sdk_java.invoke.ApiClient.execute(ApiClient.java:1111)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPostWithHttpInfo(AffiliateCreatorV202410Api.java:214)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPost(AffiliateCreatorV202410Api.java:191)
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:81)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:20:03.206",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-3",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "TikTok订单拉取任务失败，taskId=6006, pageNo=1, err=TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808092003C4D4D16A433F194153BA"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:20:03 GMT], expires=[Fri, 08 Aug 2025 01:20:03 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=31, origin; dur=12, inner; dur=3], tt_stable=[0], x-akamai-request-id=[1dc67391.1d33e15], x-cache=[TCP_MISS from a23-32-20-53.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a23-221-208-240.deploy.akamaitechnologies.com (AkamaiGHost/22.2.0-44748e1838d946f91603bfea222ba018) (-)], x-origin-response-time=[12,**************], x-parent-response-time=[43,***********], x-tt-logid=[20250808092003C4D4D16A433F194153BA], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7ee5a0b437467c2efea3c7a9a102eb7b9fa0e235b4625c51f87dc4bac5956714274a4818cb15856a3c2e69a9a9b15b4b92b074443428142352e47f4e7e16554daccee28cd7aec169f710e3687d9237f92bc93d69d4a749a15e9ac555333f978622], x-tt-trace-id=[00-250808092003C4D4D16A433F194153BA-2AA1E1636550FFC8-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}" }
                    
com.genco.common.exception.CrmebException: TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808092003C4D4D16A433F194153BA"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:20:03 GMT], expires=[Fri, 08 Aug 2025 01:20:03 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=31, origin; dur=12, inner; dur=3], tt_stable=[0], x-akamai-request-id=[1dc67391.1d33e15], x-cache=[TCP_MISS from a23-32-20-53.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a23-221-208-240.deploy.akamaitechnologies.com (AkamaiGHost/22.2.0-44748e1838d946f91603bfea222ba018) (-)], x-origin-response-time=[12,**************], x-parent-response-time=[43,***********], x-tt-logid=[20250808092003C4D4D16A433F194153BA], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7ee5a0b437467c2efea3c7a9a102eb7b9fa0e235b4625c51f87dc4bac5956714274a4818cb15856a3c2e69a9a9b15b4b92b074443428142352e47f4e7e16554daccee28cd7aec169f710e3687d9237f92bc93d69d4a749a15e9ac555333f978622], x-tt-trace-id=[00-250808092003C4D4D16A433F194153BA-2AA1E1636550FFC8-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:209)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:20:26.804",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:05.221",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-5",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808092105DD5E3B7D66BACA40A89F"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:21:05 GMT], expires=[Fri, 08 Aug 2025 01:21:05 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=33, origin; dur=12, inner; dur=3], tt_stable=[0], x-akamai-request-id=[314589be.12485e1f], x-cache=[TCP_MISS from a104-84-150-13.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a23-200-218-28.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[12,*************], x-parent-response-time=[44,*************], x-tt-logid=[20250808092105DD5E3B7D66BACA40A89F], x-tt-oec-error-level=[-], x-tt-trace-host=[013cd85c15ef3f9fe2951b700171db7a3623f3a424d48654133f96ce47cc5c8b1dd9c143ecd5d8e0ed4a8c1bd93cff9dbc0b8b98606cac6d3358bb45788672656aa2679a10fb8ec08cbbfcfdda541a2ba671031403462437626acb77e1211a3450d2a49fffce22072d6c29f5f8db3cb806], x-tt-trace-id=[00-250808092105DD5E3B7D66BACA40A89F-4018238A615ECCA3-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}" }
                    
tiktokshop.open.sdk_java.invoke.ApiException: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808092105DD5E3B7D66BACA40A89F"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:21:05 GMT], expires=[Fri, 08 Aug 2025 01:21:05 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=33, origin; dur=12, inner; dur=3], tt_stable=[0], x-akamai-request-id=[314589be.12485e1f], x-cache=[TCP_MISS from a104-84-150-13.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a23-200-218-28.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[12,*************], x-parent-response-time=[44,*************], x-tt-logid=[20250808092105DD5E3B7D66BACA40A89F], x-tt-oec-error-level=[-], x-tt-trace-host=[013cd85c15ef3f9fe2951b700171db7a3623f3a424d48654133f96ce47cc5c8b1dd9c143ecd5d8e0ed4a8c1bd93cff9dbc0b8b98606cac6d3358bb45788672656aa2679a10fb8ec08cbbfcfdda541a2ba671031403462437626acb77e1211a3450d2a49fffce22072d6c29f5f8db3cb806], x-tt-trace-id=[00-250808092105DD5E3B7D66BACA40A89F-4018238A615ECCA3-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}
	at tiktokshop.open.sdk_java.invoke.ApiClient.handleResponse(ApiClient.java:1198)
	at tiktokshop.open.sdk_java.invoke.ApiClient.execute(ApiClient.java:1111)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPostWithHttpInfo(AffiliateCreatorV202410Api.java:214)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPost(AffiliateCreatorV202410Api.java:191)
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:81)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:21:05.222",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-5",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "TikTok订单拉取任务失败，taskId=6007, pageNo=1, err=TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808092105DD5E3B7D66BACA40A89F"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:21:05 GMT], expires=[Fri, 08 Aug 2025 01:21:05 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=33, origin; dur=12, inner; dur=3], tt_stable=[0], x-akamai-request-id=[314589be.12485e1f], x-cache=[TCP_MISS from a104-84-150-13.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a23-200-218-28.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[12,*************], x-parent-response-time=[44,*************], x-tt-logid=[20250808092105DD5E3B7D66BACA40A89F], x-tt-oec-error-level=[-], x-tt-trace-host=[013cd85c15ef3f9fe2951b700171db7a3623f3a424d48654133f96ce47cc5c8b1dd9c143ecd5d8e0ed4a8c1bd93cff9dbc0b8b98606cac6d3358bb45788672656aa2679a10fb8ec08cbbfcfdda541a2ba671031403462437626acb77e1211a3450d2a49fffce22072d6c29f5f8db3cb806], x-tt-trace-id=[00-250808092105DD5E3B7D66BACA40A89F-4018238A615ECCA3-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}" }
                    
com.genco.common.exception.CrmebException: TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808092105DD5E3B7D66BACA40A89F"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:21:05 GMT], expires=[Fri, 08 Aug 2025 01:21:05 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=33, origin; dur=12, inner; dur=3], tt_stable=[0], x-akamai-request-id=[314589be.12485e1f], x-cache=[TCP_MISS from a104-84-150-13.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a23-200-218-28.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[12,*************], x-parent-response-time=[44,*************], x-tt-logid=[20250808092105DD5E3B7D66BACA40A89F], x-tt-oec-error-level=[-], x-tt-trace-host=[013cd85c15ef3f9fe2951b700171db7a3623f3a424d48654133f96ce47cc5c8b1dd9c143ecd5d8e0ed4a8c1bd93cff9dbc0b8b98606cac6d3358bb45788672656aa2679a10fb8ec08cbbfcfdda541a2ba671031403462437626acb77e1211a3450d2a49fffce22072d6c29f5f8db3cb806], x-tt-trace-id=[00-250808092105DD5E3B7D66BACA40A89F-4018238A615ECCA3-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:209)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:22:07.773",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-3",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"2025080809220701186FB993AC1340AF6C"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:22:07 GMT], expires=[Fri, 08 Aug 2025 01:22:07 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=5, cdn-cache; desc=MISS, edge; dur=1, origin; dur=51], tt_stable=[0], x-akamai-request-id=[107ed21c], x-cache=[TCP_MISS from a23-32-20-21.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-origin-response-time=[51,***********], x-tt-logid=[2025080809220701186FB993AC1340AF6C], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e1f29a5042b4b937256b6a40d0261f5d649c23bab5ec18bbe6c4e11d44df6edf7bebc37111932648636429b0bba255120a554be60b23eb579a71b1c0cbf1f98f82bdd8e353445556aa44d4fdb91332ab1], x-tt-trace-id=[00-25080809220701186FB993AC1340AF6C-2F0040D55D006630-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}" }
                    
tiktokshop.open.sdk_java.invoke.ApiException: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"2025080809220701186FB993AC1340AF6C"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:22:07 GMT], expires=[Fri, 08 Aug 2025 01:22:07 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=5, cdn-cache; desc=MISS, edge; dur=1, origin; dur=51], tt_stable=[0], x-akamai-request-id=[107ed21c], x-cache=[TCP_MISS from a23-32-20-21.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-origin-response-time=[51,***********], x-tt-logid=[2025080809220701186FB993AC1340AF6C], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e1f29a5042b4b937256b6a40d0261f5d649c23bab5ec18bbe6c4e11d44df6edf7bebc37111932648636429b0bba255120a554be60b23eb579a71b1c0cbf1f98f82bdd8e353445556aa44d4fdb91332ab1], x-tt-trace-id=[00-25080809220701186FB993AC1340AF6C-2F0040D55D006630-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}
	at tiktokshop.open.sdk_java.invoke.ApiClient.handleResponse(ApiClient.java:1198)
	at tiktokshop.open.sdk_java.invoke.ApiClient.execute(ApiClient.java:1111)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPostWithHttpInfo(AffiliateCreatorV202410Api.java:214)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPost(AffiliateCreatorV202410Api.java:191)
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:81)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:22:07.773",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-3",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "TikTok订单拉取任务失败，taskId=6008, pageNo=1, err=TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"2025080809220701186FB993AC1340AF6C"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:22:07 GMT], expires=[Fri, 08 Aug 2025 01:22:07 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=5, cdn-cache; desc=MISS, edge; dur=1, origin; dur=51], tt_stable=[0], x-akamai-request-id=[107ed21c], x-cache=[TCP_MISS from a23-32-20-21.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-origin-response-time=[51,***********], x-tt-logid=[2025080809220701186FB993AC1340AF6C], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e1f29a5042b4b937256b6a40d0261f5d649c23bab5ec18bbe6c4e11d44df6edf7bebc37111932648636429b0bba255120a554be60b23eb579a71b1c0cbf1f98f82bdd8e353445556aa44d4fdb91332ab1], x-tt-trace-id=[00-25080809220701186FB993AC1340AF6C-2F0040D55D006630-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}" }
                    
com.genco.common.exception.CrmebException: TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"2025080809220701186FB993AC1340AF6C"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:22:07 GMT], expires=[Fri, 08 Aug 2025 01:22:07 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=5, cdn-cache; desc=MISS, edge; dur=1, origin; dur=51], tt_stable=[0], x-akamai-request-id=[107ed21c], x-cache=[TCP_MISS from a23-32-20-21.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-origin-response-time=[51,***********], x-tt-logid=[2025080809220701186FB993AC1340AF6C], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e1f29a5042b4b937256b6a40d0261f5d649c23bab5ec18bbe6c4e11d44df6edf7bebc37111932648636429b0bba255120a554be60b23eb579a71b1c0cbf1f98f82bdd8e353445556aa44d4fdb91332ab1], x-tt-trace-id=[00-25080809220701186FB993AC1340AF6C-2F0040D55D006630-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:209)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:23:20.332",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-8",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"2025080809232001C72FBB3E875E413F37"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:23:20 GMT], expires=[Fri, 08 Aug 2025 01:23:20 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=3, cdn-cache; desc=MISS, edge; dur=2, origin; dur=54], tt_stable=[0], x-akamai-request-id=[f39e098], x-cache=[TCP_MISS from a104-84-150-31.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-origin-response-time=[54,*************], x-tt-logid=[2025080809232001C72FBB3E875E413F37], x-tt-oec-error-level=[-], x-tt-trace-host=[0132609f1752e91dae0224cc3a1890964cdddbbd3f824a076e6b7753f4c1ddfdcc3add087f292183419cd490cbaa6aa98fa045cb05ea9357f7db4aa2f3cce5cb59bdc593c7d041cc31d9b9355db6be0d03828c00a8903bf373c763f01aeccc6724], x-tt-trace-id=[00-25080809232001C72FBB3E875E413F37-0832EEDC24426C14-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}" }
                    
tiktokshop.open.sdk_java.invoke.ApiException: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"2025080809232001C72FBB3E875E413F37"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:23:20 GMT], expires=[Fri, 08 Aug 2025 01:23:20 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=3, cdn-cache; desc=MISS, edge; dur=2, origin; dur=54], tt_stable=[0], x-akamai-request-id=[f39e098], x-cache=[TCP_MISS from a104-84-150-31.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-origin-response-time=[54,*************], x-tt-logid=[2025080809232001C72FBB3E875E413F37], x-tt-oec-error-level=[-], x-tt-trace-host=[0132609f1752e91dae0224cc3a1890964cdddbbd3f824a076e6b7753f4c1ddfdcc3add087f292183419cd490cbaa6aa98fa045cb05ea9357f7db4aa2f3cce5cb59bdc593c7d041cc31d9b9355db6be0d03828c00a8903bf373c763f01aeccc6724], x-tt-trace-id=[00-25080809232001C72FBB3E875E413F37-0832EEDC24426C14-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}
	at tiktokshop.open.sdk_java.invoke.ApiClient.handleResponse(ApiClient.java:1198)
	at tiktokshop.open.sdk_java.invoke.ApiClient.execute(ApiClient.java:1111)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPostWithHttpInfo(AffiliateCreatorV202410Api.java:214)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPost(AffiliateCreatorV202410Api.java:191)
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:81)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:23:20.333",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-8",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "TikTok订单拉取任务失败，taskId=6009, pageNo=1, err=TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"2025080809232001C72FBB3E875E413F37"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:23:20 GMT], expires=[Fri, 08 Aug 2025 01:23:20 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=3, cdn-cache; desc=MISS, edge; dur=2, origin; dur=54], tt_stable=[0], x-akamai-request-id=[f39e098], x-cache=[TCP_MISS from a104-84-150-31.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-origin-response-time=[54,*************], x-tt-logid=[2025080809232001C72FBB3E875E413F37], x-tt-oec-error-level=[-], x-tt-trace-host=[0132609f1752e91dae0224cc3a1890964cdddbbd3f824a076e6b7753f4c1ddfdcc3add087f292183419cd490cbaa6aa98fa045cb05ea9357f7db4aa2f3cce5cb59bdc593c7d041cc31d9b9355db6be0d03828c00a8903bf373c763f01aeccc6724], x-tt-trace-id=[00-25080809232001C72FBB3E875E413F37-0832EEDC24426C14-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}" }
                    
com.genco.common.exception.CrmebException: TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"2025080809232001C72FBB3E875E413F37"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:23:20 GMT], expires=[Fri, 08 Aug 2025 01:23:20 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=3, cdn-cache; desc=MISS, edge; dur=2, origin; dur=54], tt_stable=[0], x-akamai-request-id=[f39e098], x-cache=[TCP_MISS from a104-84-150-31.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-origin-response-time=[54,*************], x-tt-logid=[2025080809232001C72FBB3E875E413F37], x-tt-oec-error-level=[-], x-tt-trace-host=[0132609f1752e91dae0224cc3a1890964cdddbbd3f824a076e6b7753f4c1ddfdcc3add087f292183419cd490cbaa6aa98fa045cb05ea9357f7db4aa2f3cce5cb59bdc593c7d041cc31d9b9355db6be0d03828c00a8903bf373c763f01aeccc6724], x-tt-trace-id=[00-25080809232001C72FBB3E875E413F37-0832EEDC24426C14-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:209)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:24:22.928",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-10",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808092423CAFA160631A94E406C12"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:24:23 GMT], expires=[Fri, 08 Aug 2025 01:24:23 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=38, origin; dur=14, inner; dur=4], tt_stable=[0], x-akamai-request-id=[cea43346.103e4e3a], x-cache=[TCP_MISS from a23-32-20-5.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a23-221-208-232.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[14,**************], x-parent-response-time=[51,**********], x-tt-logid=[20250808092423CAFA160631A94E406C12], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e9fbb785d6920e3d864d07fcfe1c509920c5ecd531a4284cc3c19280ae6b7ff6e95af78ea435e44a83034fd655818d932c053aac6b39caf7c4381fecf73726f08cfce1fc0f472a0311061b5dbd0a1185f49bb58bd0c6d137c321189e93be75450], x-tt-trace-id=[00-250808092423CAFA160631A94E406C12-703745E30AABA364-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}" }
                    
tiktokshop.open.sdk_java.invoke.ApiException: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808092423CAFA160631A94E406C12"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:24:23 GMT], expires=[Fri, 08 Aug 2025 01:24:23 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=38, origin; dur=14, inner; dur=4], tt_stable=[0], x-akamai-request-id=[cea43346.103e4e3a], x-cache=[TCP_MISS from a23-32-20-5.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a23-221-208-232.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[14,**************], x-parent-response-time=[51,**********], x-tt-logid=[20250808092423CAFA160631A94E406C12], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e9fbb785d6920e3d864d07fcfe1c509920c5ecd531a4284cc3c19280ae6b7ff6e95af78ea435e44a83034fd655818d932c053aac6b39caf7c4381fecf73726f08cfce1fc0f472a0311061b5dbd0a1185f49bb58bd0c6d137c321189e93be75450], x-tt-trace-id=[00-250808092423CAFA160631A94E406C12-703745E30AABA364-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}
	at tiktokshop.open.sdk_java.invoke.ApiClient.handleResponse(ApiClient.java:1198)
	at tiktokshop.open.sdk_java.invoke.ApiClient.execute(ApiClient.java:1111)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPostWithHttpInfo(AffiliateCreatorV202410Api.java:214)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPost(AffiliateCreatorV202410Api.java:191)
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:81)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:24:22.929",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-10",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "TikTok订单拉取任务失败，taskId=6010, pageNo=1, err=TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808092423CAFA160631A94E406C12"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:24:23 GMT], expires=[Fri, 08 Aug 2025 01:24:23 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=38, origin; dur=14, inner; dur=4], tt_stable=[0], x-akamai-request-id=[cea43346.103e4e3a], x-cache=[TCP_MISS from a23-32-20-5.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a23-221-208-232.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[14,**************], x-parent-response-time=[51,**********], x-tt-logid=[20250808092423CAFA160631A94E406C12], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e9fbb785d6920e3d864d07fcfe1c509920c5ecd531a4284cc3c19280ae6b7ff6e95af78ea435e44a83034fd655818d932c053aac6b39caf7c4381fecf73726f08cfce1fc0f472a0311061b5dbd0a1185f49bb58bd0c6d137c321189e93be75450], x-tt-trace-id=[00-250808092423CAFA160631A94E406C12-703745E30AABA364-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}" }
                    
com.genco.common.exception.CrmebException: TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808092423CAFA160631A94E406C12"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 01:24:23 GMT], expires=[Fri, 08 Aug 2025 01:24:23 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=38, origin; dur=14, inner; dur=4], tt_stable=[0], x-akamai-request-id=[cea43346.103e4e3a], x-cache=[TCP_MISS from a23-32-20-5.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a23-221-208-232.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[14,**************], x-parent-response-time=[51,**********], x-tt-logid=[20250808092423CAFA160631A94E406C12], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e9fbb785d6920e3d864d07fcfe1c509920c5ecd531a4284cc3c19280ae6b7ff6e95af78ea435e44a83034fd655818d932c053aac6b39caf7c4381fecf73726f08cfce1fc0f472a0311061b5dbd0a1185f49bb58bd0c6d137c321189e93be75450], x-tt-trace-id=[00-250808092423CAFA160631A94E406C12-703745E30AABA364-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:209)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:26:14.071",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:28:25.043",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:30:41.930",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:06.106",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:33:26.771",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:43:31.672",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:29.702",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:37.334",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:44:38.897",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:35.081",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:45:54.468",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:51:12.967",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:01.294",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:55:42.710",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:57:59.820",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:58:01.487",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:58:03.413",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:58:30.371",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:58:34.323",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:01:41.121",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:01:58.272",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:02:13.361",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:02:33.812",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:02:55.552",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:02:58.918",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:11:55.085",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:12:39.083",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:13:00.684",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:13:22.870",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:13:33.135",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:14:15.781",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-7",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"2025080810141550F45E5617134643973C"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:14:15 GMT], expires=[Fri, 08 Aug 2025 02:14:15 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=70, origin; dur=19, inner; dur=3], tt_stable=[0], x-akamai-request-id=[894e2b0a.f4ad02c], x-cache=[TCP_MISS from a23-32-20-37.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a23-52-40-85.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[20,***********], x-parent-response-time=[89,***********], x-tt-logid=[2025080810141550F45E5617134643973C], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e659fe4ea71e3d1e5bcb30f78ceac99517469a955838c21413907947b10d5a909a08c93303cecbed3202d307ef0e6a1d29069c28d344613d0734a123d66cae0064a125fda8e0f36fbb94821819d712aac3d5e58351c22f6c786ea86b68e611a8a], x-tt-trace-id=[00-25080810141550F45E5617134643973C-734B8607341E97CC-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}" }
                    
tiktokshop.open.sdk_java.invoke.ApiException: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"2025080810141550F45E5617134643973C"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:14:15 GMT], expires=[Fri, 08 Aug 2025 02:14:15 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=70, origin; dur=19, inner; dur=3], tt_stable=[0], x-akamai-request-id=[894e2b0a.f4ad02c], x-cache=[TCP_MISS from a23-32-20-37.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a23-52-40-85.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[20,***********], x-parent-response-time=[89,***********], x-tt-logid=[2025080810141550F45E5617134643973C], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e659fe4ea71e3d1e5bcb30f78ceac99517469a955838c21413907947b10d5a909a08c93303cecbed3202d307ef0e6a1d29069c28d344613d0734a123d66cae0064a125fda8e0f36fbb94821819d712aac3d5e58351c22f6c786ea86b68e611a8a], x-tt-trace-id=[00-25080810141550F45E5617134643973C-734B8607341E97CC-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}
	at tiktokshop.open.sdk_java.invoke.ApiClient.handleResponse(ApiClient.java:1198)
	at tiktokshop.open.sdk_java.invoke.ApiClient.execute(ApiClient.java:1111)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPostWithHttpInfo(AffiliateCreatorV202410Api.java:214)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPost(AffiliateCreatorV202410Api.java:191)
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:81)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.GeneratedMethodAccessor362.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:14:15.782",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-7",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "TikTok订单拉取任务失败，taskId=6060, pageNo=1, err=TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"2025080810141550F45E5617134643973C"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:14:15 GMT], expires=[Fri, 08 Aug 2025 02:14:15 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=70, origin; dur=19, inner; dur=3], tt_stable=[0], x-akamai-request-id=[894e2b0a.f4ad02c], x-cache=[TCP_MISS from a23-32-20-37.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a23-52-40-85.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[20,***********], x-parent-response-time=[89,***********], x-tt-logid=[2025080810141550F45E5617134643973C], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e659fe4ea71e3d1e5bcb30f78ceac99517469a955838c21413907947b10d5a909a08c93303cecbed3202d307ef0e6a1d29069c28d344613d0734a123d66cae0064a125fda8e0f36fbb94821819d712aac3d5e58351c22f6c786ea86b68e611a8a], x-tt-trace-id=[00-25080810141550F45E5617134643973C-734B8607341E97CC-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}" }
                    
com.genco.common.exception.CrmebException: TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"2025080810141550F45E5617134643973C"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:14:15 GMT], expires=[Fri, 08 Aug 2025 02:14:15 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=70, origin; dur=19, inner; dur=3], tt_stable=[0], x-akamai-request-id=[894e2b0a.f4ad02c], x-cache=[TCP_MISS from a23-32-20-37.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a23-52-40-85.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[20,***********], x-parent-response-time=[89,***********], x-tt-logid=[2025080810141550F45E5617134643973C], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e659fe4ea71e3d1e5bcb30f78ceac99517469a955838c21413907947b10d5a909a08c93303cecbed3202d307ef0e6a1d29069c28d344613d0734a123d66cae0064a125fda8e0f36fbb94821819d712aac3d5e58351c22f6c786ea86b68e611a8a], x-tt-trace-id=[00-25080810141550F45E5617134643973C-734B8607341E97CC-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:209)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.GeneratedMethodAccessor362.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:15:56.458",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:16:33.680",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-2",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"2025080810163312C556BAC131FA4367A0"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:16:33 GMT], expires=[Fri, 08 Aug 2025 02:16:33 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=5, cdn-cache; desc=MISS, edge; dur=1, origin; dur=56], tt_stable=[0], x-akamai-request-id=[e0c2c7d], x-cache=[TCP_MISS from a104-84-150-47.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-origin-response-time=[57,*************], x-tt-logid=[2025080810163312C556BAC131FA4367A0], x-tt-oec-error-level=[-], x-tt-trace-host=[0132609f1752e91dae0224cc3a1890964cb1d9692822f237a1644d80b0a579c2ad4a319261368cf6e045593dd14fd1e81609d0421172695936dd0fa30f23a7f61917fba00e89f316f673388a206146e8b2509f2506d51553163d4fb3fa442cbd91], x-tt-trace-id=[00-25080810163312C556BAC131FA4367A0-7FA9096838ACC55F-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}" }
                    
tiktokshop.open.sdk_java.invoke.ApiException: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"2025080810163312C556BAC131FA4367A0"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:16:33 GMT], expires=[Fri, 08 Aug 2025 02:16:33 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=5, cdn-cache; desc=MISS, edge; dur=1, origin; dur=56], tt_stable=[0], x-akamai-request-id=[e0c2c7d], x-cache=[TCP_MISS from a104-84-150-47.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-origin-response-time=[57,*************], x-tt-logid=[2025080810163312C556BAC131FA4367A0], x-tt-oec-error-level=[-], x-tt-trace-host=[0132609f1752e91dae0224cc3a1890964cb1d9692822f237a1644d80b0a579c2ad4a319261368cf6e045593dd14fd1e81609d0421172695936dd0fa30f23a7f61917fba00e89f316f673388a206146e8b2509f2506d51553163d4fb3fa442cbd91], x-tt-trace-id=[00-25080810163312C556BAC131FA4367A0-7FA9096838ACC55F-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}
	at tiktokshop.open.sdk_java.invoke.ApiClient.handleResponse(ApiClient.java:1198)
	at tiktokshop.open.sdk_java.invoke.ApiClient.execute(ApiClient.java:1111)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPostWithHttpInfo(AffiliateCreatorV202410Api.java:214)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPost(AffiliateCreatorV202410Api.java:191)
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:82)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:16:33.681",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-2",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "TikTok订单拉取任务失败，taskId=6063, pageNo=1, err=TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"2025080810163312C556BAC131FA4367A0"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:16:33 GMT], expires=[Fri, 08 Aug 2025 02:16:33 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=5, cdn-cache; desc=MISS, edge; dur=1, origin; dur=56], tt_stable=[0], x-akamai-request-id=[e0c2c7d], x-cache=[TCP_MISS from a104-84-150-47.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-origin-response-time=[57,*************], x-tt-logid=[2025080810163312C556BAC131FA4367A0], x-tt-oec-error-level=[-], x-tt-trace-host=[0132609f1752e91dae0224cc3a1890964cb1d9692822f237a1644d80b0a579c2ad4a319261368cf6e045593dd14fd1e81609d0421172695936dd0fa30f23a7f61917fba00e89f316f673388a206146e8b2509f2506d51553163d4fb3fa442cbd91], x-tt-trace-id=[00-25080810163312C556BAC131FA4367A0-7FA9096838ACC55F-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}" }
                    
com.genco.common.exception.CrmebException: TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"2025080810163312C556BAC131FA4367A0"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:16:33 GMT], expires=[Fri, 08 Aug 2025 02:16:33 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=5, cdn-cache; desc=MISS, edge; dur=1, origin; dur=56], tt_stable=[0], x-akamai-request-id=[e0c2c7d], x-cache=[TCP_MISS from a104-84-150-47.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-origin-response-time=[57,*************], x-tt-logid=[2025080810163312C556BAC131FA4367A0], x-tt-oec-error-level=[-], x-tt-trace-host=[0132609f1752e91dae0224cc3a1890964cb1d9692822f237a1644d80b0a579c2ad4a319261368cf6e045593dd14fd1e81609d0421172695936dd0fa30f23a7f61917fba00e89f316f673388a206146e8b2509f2506d51553163d4fb3fa442cbd91], x-tt-trace-id=[00-25080810163312C556BAC131FA4367A0-7FA9096838ACC55F-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:215)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:16:38.020",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.a.c.AffiliateProductV2Controller",
                    "message": "联盟产品搜索V2失败，API异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"202508081016387256F0375168B8425450"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:16:38 GMT], expires=[Fri, 08 Aug 2025 02:16:38 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=39, origin; dur=11, inner; dur=4], tt_stable=[0], x-akamai-request-id=[d53c98f9.10a7e801], x-cache=[TCP_MISS from a23-32-20-45.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a23-39-162-7.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[12,***********], x-parent-response-time=[49,***********], x-tt-logid=[202508081016387256F0375168B8425450], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e0b89c56517b5a5a97ea7070aa700c3cc9cffa6e6e6db0bb2f280f3536055174b8fd4fafefa2b54e07b3101ce3b9e123a34d7d692030c5053a8b0d1fe9d7634b5fa469b5c8ff1599f410bda3977bbc2050db83402a4a15df057e0e22dd307d3c6], x-tt-trace-id=[00-2508081016387256F0375168B8425450-5E890CD227315F70-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}" }
                    
tiktokshop.open.sdk_java.invoke.ApiException: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"202508081016387256F0375168B8425450"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:16:38 GMT], expires=[Fri, 08 Aug 2025 02:16:38 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=39, origin; dur=11, inner; dur=4], tt_stable=[0], x-akamai-request-id=[d53c98f9.10a7e801], x-cache=[TCP_MISS from a23-32-20-45.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a23-39-162-7.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[12,***********], x-parent-response-time=[49,***********], x-tt-logid=[202508081016387256F0375168B8425450], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e0b89c56517b5a5a97ea7070aa700c3cc9cffa6e6e6db0bb2f280f3536055174b8fd4fafefa2b54e07b3101ce3b9e123a34d7d692030c5053a8b0d1fe9d7634b5fa469b5c8ff1599f410bda3977bbc2050db83402a4a15df057e0e22dd307d3c6], x-tt-trace-id=[00-2508081016387256F0375168B8425450-5E890CD227315F70-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}
	at tiktokshop.open.sdk_java.invoke.ApiClient.handleResponse(ApiClient.java:1198)
	at tiktokshop.open.sdk_java.invoke.ApiClient.execute(ApiClient.java:1111)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202501Api.affiliateCreator202501SelectionProductsSearchPostWithHttpInfo(AffiliateCreatorV202501Api.java:355)
	at com.genco.service.service.impl.AffiliateProductServiceV2Impl.searchProducts(AffiliateProductServiceV2Impl.java:66)
	at com.genco.admin.controller.AffiliateProductV2Controller.searchProducts(AffiliateProductV2Controller.java:47)
	at com.genco.admin.controller.AffiliateProductV2Controller$$FastClassBySpringCGLIB$$57d87a2e.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:771)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.genco.admin.acpect.ControllerAspect.doAroundService(ControllerAspect.java:33)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
	at com.genco.admin.controller.AffiliateProductV2Controller$$EnhancerBySpringCGLIB$$16b77162.searchProducts(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:879)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:793)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:665)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.genco.admin.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:36)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:16:46.380",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-30",
                    "class": "c.g.a.c.AffiliateProductV2Controller",
                    "message": "联盟产品搜索V2失败，API异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808101646A19E3D049545D042AFEF"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:16:46 GMT], expires=[Fri, 08 Aug 2025 02:16:46 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=3, cdn-cache; desc=MISS, edge; dur=1, origin; dur=48], tt_stable=[0], x-akamai-request-id=[e0c6a0c], x-cache=[TCP_MISS from a104-84-150-47.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-origin-response-time=[48,*************], x-tt-logid=[20250808101646A19E3D049545D042AFEF], x-tt-oec-error-level=[-], x-tt-trace-host=[0132609f1752e91dae0224cc3a1890964cb1d9692822f237a1644d80b0a579c2ad4a319261368cf6e045593dd14fd1e816912d46ea2e01f219df56ebac93cc88ff79650d4a9cfe6d39319c3a9320e179b653772b9d4214120a3a02f0e3b515fb87], x-tt-trace-id=[00-250808101646A19E3D049545D042AFEF-6937DEFD3ED9F9EC-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}" }
                    
tiktokshop.open.sdk_java.invoke.ApiException: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808101646A19E3D049545D042AFEF"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:16:46 GMT], expires=[Fri, 08 Aug 2025 02:16:46 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=3, cdn-cache; desc=MISS, edge; dur=1, origin; dur=48], tt_stable=[0], x-akamai-request-id=[e0c6a0c], x-cache=[TCP_MISS from a104-84-150-47.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-origin-response-time=[48,*************], x-tt-logid=[20250808101646A19E3D049545D042AFEF], x-tt-oec-error-level=[-], x-tt-trace-host=[0132609f1752e91dae0224cc3a1890964cb1d9692822f237a1644d80b0a579c2ad4a319261368cf6e045593dd14fd1e816912d46ea2e01f219df56ebac93cc88ff79650d4a9cfe6d39319c3a9320e179b653772b9d4214120a3a02f0e3b515fb87], x-tt-trace-id=[00-250808101646A19E3D049545D042AFEF-6937DEFD3ED9F9EC-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}
	at tiktokshop.open.sdk_java.invoke.ApiClient.handleResponse(ApiClient.java:1198)
	at tiktokshop.open.sdk_java.invoke.ApiClient.execute(ApiClient.java:1111)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202501Api.affiliateCreator202501SelectionProductsSearchPostWithHttpInfo(AffiliateCreatorV202501Api.java:355)
	at com.genco.service.service.impl.AffiliateProductServiceV2Impl.searchProducts(AffiliateProductServiceV2Impl.java:66)
	at com.genco.admin.controller.AffiliateProductV2Controller.searchProducts(AffiliateProductV2Controller.java:47)
	at com.genco.admin.controller.AffiliateProductV2Controller$$FastClassBySpringCGLIB$$57d87a2e.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:771)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.genco.admin.acpect.ControllerAspect.doAroundService(ControllerAspect.java:33)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
	at com.genco.admin.controller.AffiliateProductV2Controller$$EnhancerBySpringCGLIB$$16b77162.searchProducts(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:879)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:793)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:665)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.genco.admin.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:36)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:18:22.654",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:18:38.124",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.a.c.AffiliateProductV2Controller",
                    "message": "联盟产品搜索V2失败，API异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"202508081018388416C2966B9C0F42DEFD"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:18:38 GMT], expires=[Fri, 08 Aug 2025 02:18:38 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=3, cdn-cache; desc=MISS, edge; dur=1, origin; dur=48], tt_stable=[0], x-akamai-request-id=[2176ad8f], x-cache=[TCP_MISS from a23-35-216-10.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[48,************], x-tt-logid=[202508081018388416C2966B9C0F42DEFD], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e982b6e7f5c584b587f965046e84aaa92f6f8b2bec09fbdefce2f769f674fd81cd8074a200b1e35e0aef3b1844c527b5073d24798cc74a69f4c823f62fdb372104385a0f79459a86b029e6df6602dc566], x-tt-trace-id=[00-2508081018388416C2966B9C0F42DEFD-65F823B27F132817-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}" }
                    
tiktokshop.open.sdk_java.invoke.ApiException: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"202508081018388416C2966B9C0F42DEFD"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:18:38 GMT], expires=[Fri, 08 Aug 2025 02:18:38 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=3, cdn-cache; desc=MISS, edge; dur=1, origin; dur=48], tt_stable=[0], x-akamai-request-id=[2176ad8f], x-cache=[TCP_MISS from a23-35-216-10.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[48,************], x-tt-logid=[202508081018388416C2966B9C0F42DEFD], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e982b6e7f5c584b587f965046e84aaa92f6f8b2bec09fbdefce2f769f674fd81cd8074a200b1e35e0aef3b1844c527b5073d24798cc74a69f4c823f62fdb372104385a0f79459a86b029e6df6602dc566], x-tt-trace-id=[00-2508081018388416C2966B9C0F42DEFD-65F823B27F132817-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}
	at tiktokshop.open.sdk_java.invoke.ApiClient.handleResponse(ApiClient.java:1198)
	at tiktokshop.open.sdk_java.invoke.ApiClient.execute(ApiClient.java:1111)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202501Api.affiliateCreator202501SelectionProductsSearchPostWithHttpInfo(AffiliateCreatorV202501Api.java:355)
	at com.genco.service.service.impl.AffiliateProductServiceV2Impl.searchProducts(AffiliateProductServiceV2Impl.java:66)
	at com.genco.admin.controller.AffiliateProductV2Controller.searchProducts(AffiliateProductV2Controller.java:47)
	at com.genco.admin.controller.AffiliateProductV2Controller$$FastClassBySpringCGLIB$$57d87a2e.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:771)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.genco.admin.acpect.ControllerAspect.doAroundService(ControllerAspect.java:33)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
	at com.genco.admin.controller.AffiliateProductV2Controller$$EnhancerBySpringCGLIB$$16b77162.searchProducts(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:879)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:793)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:665)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.genco.admin.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:36)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:18:45.320",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-2",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"202508081018452A261A0DA9245242E179"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:18:45 GMT], expires=[Fri, 08 Aug 2025 02:18:45 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=32, origin; dur=12, inner; dur=3], tt_stable=[0], x-akamai-request-id=[ab5e9882.217713d0], x-cache=[TCP_MISS from a23-35-216-10.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-cache-remote=[TCP_MISS from a184-28-235-53.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[12,*************], x-parent-response-time=[44,************], x-tt-logid=[202508081018452A261A0DA9245242E179], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e483dfc32118d549f0fc15eef8d7fe2dd19353376c877e143cf9fec7f031e2a79ba63b2a2b9cd9f245f42ba3a185969a4c449a9199fafad9ec001924acac1a3391948fd618f3e61b58d9e3363d49933dc32b162d70e4622ac8a65c47ccf7aa4fc], x-tt-trace-id=[00-2508081018452A261A0DA9245242E179-1157C9EB7C53E9C5-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}" }
                    
tiktokshop.open.sdk_java.invoke.ApiException: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"202508081018452A261A0DA9245242E179"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:18:45 GMT], expires=[Fri, 08 Aug 2025 02:18:45 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=32, origin; dur=12, inner; dur=3], tt_stable=[0], x-akamai-request-id=[ab5e9882.217713d0], x-cache=[TCP_MISS from a23-35-216-10.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-cache-remote=[TCP_MISS from a184-28-235-53.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[12,*************], x-parent-response-time=[44,************], x-tt-logid=[202508081018452A261A0DA9245242E179], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e483dfc32118d549f0fc15eef8d7fe2dd19353376c877e143cf9fec7f031e2a79ba63b2a2b9cd9f245f42ba3a185969a4c449a9199fafad9ec001924acac1a3391948fd618f3e61b58d9e3363d49933dc32b162d70e4622ac8a65c47ccf7aa4fc], x-tt-trace-id=[00-2508081018452A261A0DA9245242E179-1157C9EB7C53E9C5-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}
	at tiktokshop.open.sdk_java.invoke.ApiClient.handleResponse(ApiClient.java:1198)
	at tiktokshop.open.sdk_java.invoke.ApiClient.execute(ApiClient.java:1111)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPostWithHttpInfo(AffiliateCreatorV202410Api.java:214)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPost(AffiliateCreatorV202410Api.java:191)
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:82)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:18:45.322",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-2",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "TikTok订单拉取任务失败，taskId=6066, pageNo=1, err=TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"202508081018452A261A0DA9245242E179"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:18:45 GMT], expires=[Fri, 08 Aug 2025 02:18:45 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=32, origin; dur=12, inner; dur=3], tt_stable=[0], x-akamai-request-id=[ab5e9882.217713d0], x-cache=[TCP_MISS from a23-35-216-10.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-cache-remote=[TCP_MISS from a184-28-235-53.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[12,*************], x-parent-response-time=[44,************], x-tt-logid=[202508081018452A261A0DA9245242E179], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e483dfc32118d549f0fc15eef8d7fe2dd19353376c877e143cf9fec7f031e2a79ba63b2a2b9cd9f245f42ba3a185969a4c449a9199fafad9ec001924acac1a3391948fd618f3e61b58d9e3363d49933dc32b162d70e4622ac8a65c47ccf7aa4fc], x-tt-trace-id=[00-2508081018452A261A0DA9245242E179-1157C9EB7C53E9C5-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}" }
                    
com.genco.common.exception.CrmebException: TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"202508081018452A261A0DA9245242E179"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:18:45 GMT], expires=[Fri, 08 Aug 2025 02:18:45 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=32, origin; dur=12, inner; dur=3], tt_stable=[0], x-akamai-request-id=[ab5e9882.217713d0], x-cache=[TCP_MISS from a23-35-216-10.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-cache-remote=[TCP_MISS from a184-28-235-53.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[12,*************], x-parent-response-time=[44,************], x-tt-logid=[202508081018452A261A0DA9245242E179], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e483dfc32118d549f0fc15eef8d7fe2dd19353376c877e143cf9fec7f031e2a79ba63b2a2b9cd9f245f42ba3a185969a4c449a9199fafad9ec001924acac1a3391948fd618f3e61b58d9e3363d49933dc32b162d70e4622ac8a65c47ccf7aa4fc], x-tt-trace-id=[00-2508081018452A261A0DA9245242E179-1157C9EB7C53E9C5-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:215)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:19:46.627",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808101946DA645791994CF842CC12"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:19:46 GMT], expires=[Fri, 08 Aug 2025 02:19:46 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=4, cdn-cache; desc=MISS, edge; dur=0, origin; dur=53], tt_stable=[0], x-akamai-request-id=[217a241b], x-cache=[TCP_MISS from a23-35-216-10.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[53,************], x-tt-logid=[20250808101946DA645791994CF842CC12], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e982b6e7f5c584b587f965046e84aaa92f6f8b2bec09fbdefce2f769f674fd81ca6e4272acd8c5ef43b90c6253780422a4aa160f71c403ed74bfc4a745781df145b01ec01f61a8d5aeb467046de4ec8ae], x-tt-trace-id=[00-250808101946DA645791994CF842CC12-70059CB45033A93A-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}" }
                    
tiktokshop.open.sdk_java.invoke.ApiException: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808101946DA645791994CF842CC12"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:19:46 GMT], expires=[Fri, 08 Aug 2025 02:19:46 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=4, cdn-cache; desc=MISS, edge; dur=0, origin; dur=53], tt_stable=[0], x-akamai-request-id=[217a241b], x-cache=[TCP_MISS from a23-35-216-10.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[53,************], x-tt-logid=[20250808101946DA645791994CF842CC12], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e982b6e7f5c584b587f965046e84aaa92f6f8b2bec09fbdefce2f769f674fd81ca6e4272acd8c5ef43b90c6253780422a4aa160f71c403ed74bfc4a745781df145b01ec01f61a8d5aeb467046de4ec8ae], x-tt-trace-id=[00-250808101946DA645791994CF842CC12-70059CB45033A93A-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}
	at tiktokshop.open.sdk_java.invoke.ApiClient.handleResponse(ApiClient.java:1198)
	at tiktokshop.open.sdk_java.invoke.ApiClient.execute(ApiClient.java:1111)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPostWithHttpInfo(AffiliateCreatorV202410Api.java:214)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPost(AffiliateCreatorV202410Api.java:191)
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:82)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:19:46.628",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "TikTok订单拉取任务失败，taskId=6068, pageNo=1, err=TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808101946DA645791994CF842CC12"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:19:46 GMT], expires=[Fri, 08 Aug 2025 02:19:46 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=4, cdn-cache; desc=MISS, edge; dur=0, origin; dur=53], tt_stable=[0], x-akamai-request-id=[217a241b], x-cache=[TCP_MISS from a23-35-216-10.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[53,************], x-tt-logid=[20250808101946DA645791994CF842CC12], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e982b6e7f5c584b587f965046e84aaa92f6f8b2bec09fbdefce2f769f674fd81ca6e4272acd8c5ef43b90c6253780422a4aa160f71c403ed74bfc4a745781df145b01ec01f61a8d5aeb467046de4ec8ae], x-tt-trace-id=[00-250808101946DA645791994CF842CC12-70059CB45033A93A-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}" }
                    
com.genco.common.exception.CrmebException: TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808101946DA645791994CF842CC12"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:19:46 GMT], expires=[Fri, 08 Aug 2025 02:19:46 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=4, cdn-cache; desc=MISS, edge; dur=0, origin; dur=53], tt_stable=[0], x-akamai-request-id=[217a241b], x-cache=[TCP_MISS from a23-35-216-10.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[53,************], x-tt-logid=[20250808101946DA645791994CF842CC12], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e982b6e7f5c584b587f965046e84aaa92f6f8b2bec09fbdefce2f769f674fd81ca6e4272acd8c5ef43b90c6253780422a4aa160f71c403ed74bfc4a745781df145b01ec01f61a8d5aeb467046de4ec8ae], x-tt-trace-id=[00-250808101946DA645791994CF842CC12-70059CB45033A93A-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:215)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:21:48.375",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-6",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"202508081021481898718D6708A0441F9A"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:21:48 GMT], expires=[Fri, 08 Aug 2025 02:21:48 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=3, cdn-cache; desc=MISS, edge; dur=0, origin; dur=51], tt_stable=[0], x-akamai-request-id=[21803842], x-cache=[TCP_MISS from a23-35-216-10.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[51,************], x-tt-logid=[202508081021481898718D6708A0441F9A], x-tt-oec-error-level=[-], x-tt-trace-host=[0195bc03fd284874c0fd7c0c3b237caf11ffaef6996186ad9df4a28c654eb04c19cfcaa7275c48aadd01733b7a6e789c39419b529700015426613bb23c97cfc865b1d4349428a2a49542427a1fedd5d8bec4360056b2c1f8fc4108e4ae696068d7], x-tt-trace-id=[00-2508081021481898718D6708A0441F9A-102C1B3646B466B8-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}" }
                    
tiktokshop.open.sdk_java.invoke.ApiException: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"202508081021481898718D6708A0441F9A"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:21:48 GMT], expires=[Fri, 08 Aug 2025 02:21:48 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=3, cdn-cache; desc=MISS, edge; dur=0, origin; dur=51], tt_stable=[0], x-akamai-request-id=[21803842], x-cache=[TCP_MISS from a23-35-216-10.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[51,************], x-tt-logid=[202508081021481898718D6708A0441F9A], x-tt-oec-error-level=[-], x-tt-trace-host=[0195bc03fd284874c0fd7c0c3b237caf11ffaef6996186ad9df4a28c654eb04c19cfcaa7275c48aadd01733b7a6e789c39419b529700015426613bb23c97cfc865b1d4349428a2a49542427a1fedd5d8bec4360056b2c1f8fc4108e4ae696068d7], x-tt-trace-id=[00-2508081021481898718D6708A0441F9A-102C1B3646B466B8-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}
	at tiktokshop.open.sdk_java.invoke.ApiClient.handleResponse(ApiClient.java:1198)
	at tiktokshop.open.sdk_java.invoke.ApiClient.execute(ApiClient.java:1111)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPostWithHttpInfo(AffiliateCreatorV202410Api.java:214)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPost(AffiliateCreatorV202410Api.java:191)
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:82)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:21:48.375",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-6",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "TikTok订单拉取任务失败，taskId=6071, pageNo=1, err=TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"202508081021481898718D6708A0441F9A"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:21:48 GMT], expires=[Fri, 08 Aug 2025 02:21:48 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=3, cdn-cache; desc=MISS, edge; dur=0, origin; dur=51], tt_stable=[0], x-akamai-request-id=[21803842], x-cache=[TCP_MISS from a23-35-216-10.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[51,************], x-tt-logid=[202508081021481898718D6708A0441F9A], x-tt-oec-error-level=[-], x-tt-trace-host=[0195bc03fd284874c0fd7c0c3b237caf11ffaef6996186ad9df4a28c654eb04c19cfcaa7275c48aadd01733b7a6e789c39419b529700015426613bb23c97cfc865b1d4349428a2a49542427a1fedd5d8bec4360056b2c1f8fc4108e4ae696068d7], x-tt-trace-id=[00-2508081021481898718D6708A0441F9A-102C1B3646B466B8-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}" }
                    
com.genco.common.exception.CrmebException: TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"202508081021481898718D6708A0441F9A"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:21:48 GMT], expires=[Fri, 08 Aug 2025 02:21:48 GMT], pragma=[no-cache], server=[TLB], server-timing=[inner; dur=3, cdn-cache; desc=MISS, edge; dur=0, origin; dur=51], tt_stable=[0], x-akamai-request-id=[21803842], x-cache=[TCP_MISS from a23-35-216-10.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[51,************], x-tt-logid=[202508081021481898718D6708A0441F9A], x-tt-oec-error-level=[-], x-tt-trace-host=[0195bc03fd284874c0fd7c0c3b237caf11ffaef6996186ad9df4a28c654eb04c19cfcaa7275c48aadd01733b7a6e789c39419b529700015426613bb23c97cfc865b1d4349428a2a49542427a1fedd5d8bec4360056b2c1f8fc4108e4ae696068d7], x-tt-trace-id=[00-2508081021481898718D6708A0441F9A-102C1B3646B466B8-00], x-tt-trace-tag=[id=16;cdn-cache=hit;type=dyn]}
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:215)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:23:32.004",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:23:50.012",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-4",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808102350904BD1FA3F50E837DAB9"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:23:50 GMT], expires=[Fri, 08 Aug 2025 02:23:50 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=36, origin; dur=16, inner; dur=6], tt_stable=[0], x-akamai-request-id=[d6f5ab86.218674d6], x-cache=[TCP_MISS from a23-35-216-10.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-cache-remote=[TCP_MISS from a23-54-124-213.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[16,*************], x-parent-response-time=[52,************], x-tt-logid=[20250808102350904BD1FA3F50E837DAB9], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e778359e2cbc5ca1c1b90d1d0c8e4362c216b13c22292b7fd37ea446d6949beef98d990469df32d564c0416da42b267749f8c0d9556f39bf769d8cc700f91e445b110df9eabf60575e7d7a4880a9b9818edbe417d76e738918c0f0896aafde0cc], x-tt-trace-id=[00-250808102350904BD1FA3F50E837DAB9-3A63B94B5CE9868D-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}" }
                    
tiktokshop.open.sdk_java.invoke.ApiException: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808102350904BD1FA3F50E837DAB9"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:23:50 GMT], expires=[Fri, 08 Aug 2025 02:23:50 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=36, origin; dur=16, inner; dur=6], tt_stable=[0], x-akamai-request-id=[d6f5ab86.218674d6], x-cache=[TCP_MISS from a23-35-216-10.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-cache-remote=[TCP_MISS from a23-54-124-213.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[16,*************], x-parent-response-time=[52,************], x-tt-logid=[20250808102350904BD1FA3F50E837DAB9], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e778359e2cbc5ca1c1b90d1d0c8e4362c216b13c22292b7fd37ea446d6949beef98d990469df32d564c0416da42b267749f8c0d9556f39bf769d8cc700f91e445b110df9eabf60575e7d7a4880a9b9818edbe417d76e738918c0f0896aafde0cc], x-tt-trace-id=[00-250808102350904BD1FA3F50E837DAB9-3A63B94B5CE9868D-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}
	at tiktokshop.open.sdk_java.invoke.ApiClient.handleResponse(ApiClient.java:1198)
	at tiktokshop.open.sdk_java.invoke.ApiClient.execute(ApiClient.java:1111)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPostWithHttpInfo(AffiliateCreatorV202410Api.java:214)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPost(AffiliateCreatorV202410Api.java:191)
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:82)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:23:50.012",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-4",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "TikTok订单拉取任务失败，taskId=6074, pageNo=1, err=TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808102350904BD1FA3F50E837DAB9"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:23:50 GMT], expires=[Fri, 08 Aug 2025 02:23:50 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=36, origin; dur=16, inner; dur=6], tt_stable=[0], x-akamai-request-id=[d6f5ab86.218674d6], x-cache=[TCP_MISS from a23-35-216-10.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-cache-remote=[TCP_MISS from a23-54-124-213.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[16,*************], x-parent-response-time=[52,************], x-tt-logid=[20250808102350904BD1FA3F50E837DAB9], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e778359e2cbc5ca1c1b90d1d0c8e4362c216b13c22292b7fd37ea446d6949beef98d990469df32d564c0416da42b267749f8c0d9556f39bf769d8cc700f91e445b110df9eabf60575e7d7a4880a9b9818edbe417d76e738918c0f0896aafde0cc], x-tt-trace-id=[00-250808102350904BD1FA3F50E837DAB9-3A63B94B5CE9868D-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}" }
                    
com.genco.common.exception.CrmebException: TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808102350904BD1FA3F50E837DAB9"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:23:50 GMT], expires=[Fri, 08 Aug 2025 02:23:50 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=36, origin; dur=16, inner; dur=6], tt_stable=[0], x-akamai-request-id=[d6f5ab86.218674d6], x-cache=[TCP_MISS from a23-35-216-10.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-cache-remote=[TCP_MISS from a23-54-124-213.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[16,*************], x-parent-response-time=[52,************], x-tt-logid=[20250808102350904BD1FA3F50E837DAB9], x-tt-oec-error-level=[-], x-tt-trace-host=[0130da47590bdd0034912c59a1db4fbd7e778359e2cbc5ca1c1b90d1d0c8e4362c216b13c22292b7fd37ea446d6949beef98d990469df32d564c0416da42b267749f8c0d9556f39bf769d8cc700f91e445b110df9eabf60575e7d7a4880a9b9818edbe417d76e738918c0f0896aafde0cc], x-tt-trace-id=[00-250808102350904BD1FA3F50E837DAB9-3A63B94B5CE9868D-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:215)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:24:01.921",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:24:11.499",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:25:51.492",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-9",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808102551A28B98E0BC698843654B"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:25:51 GMT], expires=[Fri, 08 Aug 2025 02:25:51 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=32, origin; dur=14, inner; dur=5], tt_stable=[0], x-akamai-request-id=[8a43518e.136e3aa8], x-cache=[TCP_MISS from a184-26-91-223.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-cache-remote=[TCP_MISS from a184-28-235-6.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[14,************], x-parent-response-time=[46,*************], x-tt-logid=[20250808102551A28B98E0BC698843654B], x-tt-oec-error-level=[-], x-tt-trace-host=[0132609f1752e91dae0224cc3a1890964c08b730337b63f4f9514be1c2b7fb03412478461d9d2453e2b6b1b4d8db80dbe64894d41ff5b1390fa7b526908fff4bc733243fccb01569d7fbda15c9966193a26361f1ea140412120184f4436929f242d82d3ed36017078d519e007836c5acf3], x-tt-trace-id=[00-250808102551A28B98E0BC698843654B-40A54CD56BF7F346-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}" }
                    
tiktokshop.open.sdk_java.invoke.ApiException: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808102551A28B98E0BC698843654B"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:25:51 GMT], expires=[Fri, 08 Aug 2025 02:25:51 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=32, origin; dur=14, inner; dur=5], tt_stable=[0], x-akamai-request-id=[8a43518e.136e3aa8], x-cache=[TCP_MISS from a184-26-91-223.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-cache-remote=[TCP_MISS from a184-28-235-6.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[14,************], x-parent-response-time=[46,*************], x-tt-logid=[20250808102551A28B98E0BC698843654B], x-tt-oec-error-level=[-], x-tt-trace-host=[0132609f1752e91dae0224cc3a1890964c08b730337b63f4f9514be1c2b7fb03412478461d9d2453e2b6b1b4d8db80dbe64894d41ff5b1390fa7b526908fff4bc733243fccb01569d7fbda15c9966193a26361f1ea140412120184f4436929f242d82d3ed36017078d519e007836c5acf3], x-tt-trace-id=[00-250808102551A28B98E0BC698843654B-40A54CD56BF7F346-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}
	at tiktokshop.open.sdk_java.invoke.ApiClient.handleResponse(ApiClient.java:1198)
	at tiktokshop.open.sdk_java.invoke.ApiClient.execute(ApiClient.java:1111)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPostWithHttpInfo(AffiliateCreatorV202410Api.java:214)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPost(AffiliateCreatorV202410Api.java:191)
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:82)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:25:51.492",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-9",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "TikTok订单拉取任务失败，taskId=6077, pageNo=1, err=TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808102551A28B98E0BC698843654B"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:25:51 GMT], expires=[Fri, 08 Aug 2025 02:25:51 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=32, origin; dur=14, inner; dur=5], tt_stable=[0], x-akamai-request-id=[8a43518e.136e3aa8], x-cache=[TCP_MISS from a184-26-91-223.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-cache-remote=[TCP_MISS from a184-28-235-6.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[14,************], x-parent-response-time=[46,*************], x-tt-logid=[20250808102551A28B98E0BC698843654B], x-tt-oec-error-level=[-], x-tt-trace-host=[0132609f1752e91dae0224cc3a1890964c08b730337b63f4f9514be1c2b7fb03412478461d9d2453e2b6b1b4d8db80dbe64894d41ff5b1390fa7b526908fff4bc733243fccb01569d7fbda15c9966193a26361f1ea140412120184f4436929f242d82d3ed36017078d519e007836c5acf3], x-tt-trace-id=[00-250808102551A28B98E0BC698843654B-40A54CD56BF7F346-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}" }
                    
com.genco.common.exception.CrmebException: TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"20250808102551A28B98E0BC698843654B"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:25:51 GMT], expires=[Fri, 08 Aug 2025 02:25:51 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=32, origin; dur=14, inner; dur=5], tt_stable=[0], x-akamai-request-id=[8a43518e.136e3aa8], x-cache=[TCP_MISS from a184-26-91-223.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-cache-remote=[TCP_MISS from a184-28-235-6.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[14,************], x-parent-response-time=[46,*************], x-tt-logid=[20250808102551A28B98E0BC698843654B], x-tt-oec-error-level=[-], x-tt-trace-host=[0132609f1752e91dae0224cc3a1890964c08b730337b63f4f9514be1c2b7fb03412478461d9d2453e2b6b1b4d8db80dbe64894d41ff5b1390fa7b526908fff4bc733243fccb01569d7fbda15c9966193a26361f1ea140412120184f4436929f242d82d3ed36017078d519e007836c5acf3], x-tt-trace-id=[00-250808102551A28B98E0BC698843654B-40A54CD56BF7F346-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:215)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:27:24.415",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:27:33.731",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:27:45.398",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:28:15.252",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:28:16.560",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:28:55.006",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-6",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"2025080810285571793F371A5CFF432962"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:28:55 GMT], expires=[Fri, 08 Aug 2025 02:28:55 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=33, origin; dur=11, inner; dur=4], tt_stable=[0], x-akamai-request-id=[9f968cab.2f24401], x-cache=[TCP_MISS from a104-84-150-128.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a184-28-235-14.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[11,*************], x-parent-response-time=[43,**************], x-tt-logid=[2025080810285571793F371A5CFF432962], x-tt-oec-error-level=[-], x-tt-trace-host=[0132609f1752e91dae0224cc3a1890964cae211242c81b8b5223a8f9ed034b6bc80351d08a3615a23c0c3e674df2bdb05d8d07144ce9aa060cdfe264b3237bd537f3e864b89e505c9f41893dee52651ff9b69500d0d7b5f1d3753b5997d3b6e367cb750628b49012668383c70471da0d36], x-tt-trace-id=[00-25080810285571793F371A5CFF432962-65223E3F1E609331-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}" }
                    
tiktokshop.open.sdk_java.invoke.ApiException: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"2025080810285571793F371A5CFF432962"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:28:55 GMT], expires=[Fri, 08 Aug 2025 02:28:55 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=33, origin; dur=11, inner; dur=4], tt_stable=[0], x-akamai-request-id=[9f968cab.2f24401], x-cache=[TCP_MISS from a104-84-150-128.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a184-28-235-14.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[11,*************], x-parent-response-time=[43,**************], x-tt-logid=[2025080810285571793F371A5CFF432962], x-tt-oec-error-level=[-], x-tt-trace-host=[0132609f1752e91dae0224cc3a1890964cae211242c81b8b5223a8f9ed034b6bc80351d08a3615a23c0c3e674df2bdb05d8d07144ce9aa060cdfe264b3237bd537f3e864b89e505c9f41893dee52651ff9b69500d0d7b5f1d3753b5997d3b6e367cb750628b49012668383c70471da0d36], x-tt-trace-id=[00-25080810285571793F371A5CFF432962-65223E3F1E609331-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}
	at tiktokshop.open.sdk_java.invoke.ApiClient.handleResponse(ApiClient.java:1198)
	at tiktokshop.open.sdk_java.invoke.ApiClient.execute(ApiClient.java:1111)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPostWithHttpInfo(AffiliateCreatorV202410Api.java:214)
	at tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPost(AffiliateCreatorV202410Api.java:191)
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:82)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:28:55.007",
                    "level": "ERROR",
                    "thread": "crmeb-scheduled-task-pool-6",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "TikTok订单拉取任务失败，taskId=6081, pageNo=1, err=TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"2025080810285571793F371A5CFF432962"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:28:55 GMT], expires=[Fri, 08 Aug 2025 02:28:55 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=33, origin; dur=11, inner; dur=4], tt_stable=[0], x-akamai-request-id=[9f968cab.2f24401], x-cache=[TCP_MISS from a104-84-150-128.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a184-28-235-14.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[11,*************], x-parent-response-time=[43,**************], x-tt-logid=[2025080810285571793F371A5CFF432962], x-tt-oec-error-level=[-], x-tt-trace-host=[0132609f1752e91dae0224cc3a1890964cae211242c81b8b5223a8f9ed034b6bc80351d08a3615a23c0c3e674df2bdb05d8d07144ce9aa060cdfe264b3237bd537f3e864b89e505c9f41893dee52651ff9b69500d0d7b5f1d3753b5997d3b6e367cb750628b49012668383c70471da0d36], x-tt-trace-id=[00-25080810285571793F371A5CFF432962-65223E3F1E609331-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}" }
                    
com.genco.common.exception.CrmebException: TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: Message: 
HTTP response code: 401
HTTP response body: {"code":105002,"message":"Expired credentials. The 'access_token' or 'x-tts-access-token' header has expired. For more details: https://m.tiktok.shop/s/AIu6dbFhs2XW","request_id":"2025080810285571793F371A5CFF432962"}
HTTP response headers: {cache-control=[max-age=0, no-cache, no-store], content-length=[216], content-type=[application/json; charset=utf-8], date=[Fri, 08 Aug 2025 02:28:55 GMT], expires=[Fri, 08 Aug 2025 02:28:55 GMT], pragma=[no-cache], server=[TLB], server-timing=[cdn-cache; desc=MISS, edge; dur=33, origin; dur=11, inner; dur=4], tt_stable=[0], x-akamai-request-id=[9f968cab.2f24401], x-cache=[TCP_MISS from a104-84-150-128.deploy.akamaitechnologies.com (AkamaiGHost/22.2.2-1df3f50fb263534fbc9222a3273eb851) (-)], x-cache-remote=[TCP_MISS from a184-28-235-14.deploy.akamaitechnologies.com (AkamaiGHost/22.2.1-0c3fcf3f6cb3abc59f338b5096edf6e9) (-)], x-origin-response-time=[11,*************], x-parent-response-time=[43,**************], x-tt-logid=[2025080810285571793F371A5CFF432962], x-tt-oec-error-level=[-], x-tt-trace-host=[0132609f1752e91dae0224cc3a1890964cae211242c81b8b5223a8f9ed034b6bc80351d08a3615a23c0c3e674df2bdb05d8d07144ce9aa060cdfe264b3237bd537f3e864b89e505c9f41893dee52651ff9b69500d0d7b5f1d3753b5997d3b6e367cb750628b49012668383c70471da0d36], x-tt-trace-id=[00-25080810285571793F371A5CFF432962-65223E3F1E609331-00], x-tt-trace-tag=[id=16;cdn-cache=miss;type=dyn]}
	at com.genco.service.service.impl.TiktokOrderSyncServiceImpl.syncTiktokOrders(TiktokOrderSyncServiceImpl.java:215)
	at com.genco.admin.task.order.OrderTiktokPullTask.pullTiktokOrders(OrderTiktokPullTask.java:86)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:29:19.248",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:29:22.305",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:29:26.603",
                    "level": "ERROR",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.a.c.c.C.[.[.[/].[dispatcherServlet]",
                    "message": "Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception" }
                    
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
