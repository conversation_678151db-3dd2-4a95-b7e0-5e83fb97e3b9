<template>
  <div class="divBox relative">
    <el-card class="box-card">
      <div class="container mt-1">
        <el-form v-model="userFrom" inline size="small">
          <el-form-item :label="$t('affiliateProducts.keywords')">
            <el-input
              v-model="userFrom.keywords"
              placeholder="支持手机号、昵称搜索"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item :label="$t('user.center.userLevel') + '：'">
            <el-select
              v-model="levelData"
              :placeholder="$t('common.pleaseSelect')"
              class="selWidth"
              clearable
              filterable
              multiple
            >
              <el-option
                :value="item.id"
                v-for="(item, index) in levelList"
                :key="index"
                :label="item.name"
              >
                <span style="float: left">{{ item.name }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">
                  {{ getUpgradeTypeText(item.upgradeType) }}
                  <span v-if="item.upgradeType === 1"> - Rp {{ item.upgradePrice }}</span>
                </span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <el-button size="small" type="primary" class="mr10" @click="getList(1)">
        {{ $t("common.query") }}
      </el-button>

      <el-button size="small" type="" class="mr10" @click="resetForm()">
        {{ $t("common.reset") }}
      </el-button>
    </el-card>

    <el-card class="box-card" style="margin-top: 12px;">
      <el-table
        v-loading="loading"
        :data="tableData"
        size="small"
        :header-cell-style="{ fontWeight: 'bold' }"
      >
        <el-table-column
          :label="$t('common.serialNumber')"
          type="index"
          width="110"
        ></el-table-column>
        <el-table-column :label="$t('user.center.avatar')" min-width="80">
          <template slot-scope="scope">
            <div class="demo-image__preview">
              <el-image
                style="width: 36px; height: 36px"
                :src="scope.row.avatar"
                :preview-src-list="[scope.row.avatar]"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('user.center.nickname')"
          min-width="150"
          prop="nickname"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.nickname | filterEmpty }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column :label="$t('user.center.tiktokAccount')" min-width="80"></el-table-column> -->
        <el-table-column :label="$t('user.center.tiktokId')" min-width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.openId | filterEmpty }}</span>
          </template></el-table-column
        >
        <el-table-column :label="$t('user.center.phone')" min-width="100">
          <template slot-scope="scope">
            <span>{{ scope.row.phone | filterEmpty }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('user.center.whatsApp')" min-width="80">
          <template slot-scope="scope">
            <span>{{ scope.row.whatsAppAccount | filterEmpty }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('user.center.registerTime')"
          min-width="150"
          prop="createTime"
        ></el-table-column>
        <el-table-column
          :label="$t('user.center.lastLoginTime')"
          min-width="150"
          prop="lastLoginTime"
        ></el-table-column>
        <el-table-column
          :label="$t('user.center.orderCount')"
          min-width="80"
          prop="payCount"
        ></el-table-column>
        <el-table-column
          :label="$t('user.center.orderFinishCount')"
          min-width="80"
          prop="spreadCount"
        ></el-table-column>
        <el-table-column :label="$t('user.center.isAgent')" min-width="80">
          <template slot-scope="scope">
            <span>{{ scope.row.isAgent | filterEmpty }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('user.center.isPartner')" min-width="80">
          <template slot-scope="scope">
            <span>{{ scope.row.isPartner | filterEmpty }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('user.center.userLevelLabel')"
          min-width="80"
        >
          <template slot-scope="scope">
            <span>{{
              handlelevelFilter(scope.row.level) | filterEmpty
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('user.center.inviter')"
          min-width="80"
          prop="spreadNickname"
        ></el-table-column>
      </el-table>
      <el-pagination
        class="mt20"
        @size-change="sizeChange"
        @current-change="pageChange"
        :current-page="userFrom.page"
        :page-sizes="[20, 40, 60, 100]"
        :page-size="userFrom.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="userFrom.total"
      >
      </el-pagination>
    </el-card>
  </div>
</template>

<script>
import { userListApi, levelListApi } from "@/api/user";
export default {
  name: "UserCenter",
  data() {
    return {
      userFrom: {
        keywords: "",
        level: [],
        page: 1,
        limit: 20,
        total: 0
      },
      tableData: [],
      levelList: [],
      levelData: [],
      loading: false
    };
  },
  created() {},
  mounted() {
    this.getLevelList();
  },
  methods: {
    handlelevelFilter(status) {
      if (!String(status) && status !== 0) {
        return "";
      }
      let array = this.levelList.filter(item => status === item.grade);
      if (array.length) {
        return array[0].name;
      } else {
        return "";
      }
    },
    // 列表
    getList(num) {
      this.loading = true;
      this.userFrom.page = num ? num : this.userFrom.page;

      // 将等级ID转换为对应的grade值
      if (this.levelData.length > 0) {
        const gradeValues = this.levelData.map(levelId => {
          const level = this.levelList.find(item => item.id === levelId);
          return level ? level.grade : null;
        }).filter(grade => grade !== null);
        this.userFrom.level = gradeValues.join(",");
      } else {
        this.userFrom.level = "";
      }

      if (this.loginType == 0) this.userFrom.userType = "";
      userListApi(this.userFrom)
        .then(res => {
          this.tableData = res.list;
          this.userFrom.total = res.total;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
      this.checkedCities = this.$cache.local.has("user_stroge")
        ? this.$cache.local.getJSON("user_stroge")
        : this.checkedCities;
    }, //切换页数
    pageChange(index) {
      this.userFrom.page = index;
      this.getList();
    },
    //切换显示条数
    sizeChange(index) {
      this.userFrom.limit = index;
      this.getList();
    },
    resetForm() {
      this.userFrom = {
        keywords: "",
        level: [],
        page: 1,
        limit: 20,
        total: 0
      };
      this.levelData = [];
      this.getList();
    },
    // 获取等级列表
    getLevelList() {
      levelListApi().then(res => {
        this.levelList = res;
        // 存储到 localStorage 供 levelFilter 使用
        localStorage.setItem('levelKey', JSON.stringify(res));
        // 等级数据加载完成后再加载用户列表
        this.getList();
      });
    },
    // 获取升级方式文本
    getUpgradeTypeText(type) {
      return this.$t(`user.grade.upgradeTypes.${type}`) || this.$t('common.unknown');
    }
  }
};
</script>
<style scoped lang="scss">
/**/
</style>
