package com.genco.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.genco.common.constants.SysConfigConstants;
import com.genco.common.exception.CrmebException;
import com.genco.common.model.order.StoreOrder;
import com.genco.common.model.order.StoreOrderInfo;
import com.genco.common.model.product.StoreProduct;
import com.genco.common.model.user.User;
import com.genco.common.utils.DateUtil;
import com.genco.common.utils.OrderUtil;
import com.genco.service.service.*;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api;
import tiktokshop.open.sdk_java.invoke.ApiClient;
import tiktokshop.open.sdk_java.model.AffiliateCreator.V202410.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class TiktokOrderSyncServiceImpl implements TiktokOrderSyncService, PlatformOrderSyncService {
    private static final Logger logger = LoggerFactory.getLogger(TiktokOrderSyncServiceImpl.class);

    @Autowired
    private StoreOrderService storeOrderService;

    @Autowired
    private StoreOrderInfoService storeOrderInfoService;

    @Autowired
    private StoreProductService storeProductService;

    @Autowired
    private UserService userService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private UserRewardTaskService userRewardTaskService;


    @Override
    public String syncTiktokOrders(String pageToken, Date startTime, Date endTime) {
        logger.info("开始同步TikTok订单 - pageToken: {}, startTime: {}, endTime: {}",
                pageToken, startTime, endTime);

        ApiClient apiClient = new ApiClient();
        apiClient.setAppkey(systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_APP_KEY));
        apiClient.setSecret(systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_APP_SECRET));
        apiClient.setTokens(systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_ACCESS_TOKEN));
        logger.debug("TikTok API客户端初始化完成");

        AffiliateCreatorV202410Api affiliateCreatorV202410Api = new AffiliateCreatorV202410Api(apiClient);
        SearchCreatorAffiliateOrdersResponse resp = null;
        String nextPageToken = null;

        try {
            logger.debug("开始构建API请求参数");
            SearchCreatorAffiliateOrdersRequestBody requestBody = new SearchCreatorAffiliateOrdersRequestBody();
            if (startTime != null && endTime != null) {
                requestBody.setCreateTimeGe(startTime.getTime() / 1000);
                requestBody.setCreateTimeLt(endTime.getTime() / 1000);
                logger.debug("设置时间范围: {} 至 {}", startTime, endTime);
            }

            logger.info("开始调用TikTok API获取订单数据");
            resp = affiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPost(
                    20L, apiClient.getTokens(), "application/json", pageToken, requestBody);
            logger.debug("TikTok API调用完成，响应码: {}", resp != null ? resp.getCode() : "null");

            if (resp != null && resp.getCode() != null && resp.getCode() == 0) {
                SearchCreatorAffiliateOrdersResponseData respData = resp.getData();
                nextPageToken = respData.getNextPageToken();
                logger.info("API调用成功，获取到下一页token: {}, 开始处理订单数据", nextPageToken);

                Boolean execute = transactionTemplate.execute(e -> {
                    List<SearchCreatorAffiliateOrdersResponseDataOrders> orders = respData.getOrders();
                    if (orders == null) {
                        logger.error("订单同步结果查询异常 - orders为null");
                        throw new CrmebException("订单同步结果查询异常");
                    }

                    logger.info("开始处理 {} 个订单", orders.size());
                    int processedCount = 0;
                    int updatedCount = 0;
                    int skippedCount = 0;
                    int errorCount = 0;

                    StoreOrder storeOrder = new StoreOrder();
                    for (SearchCreatorAffiliateOrdersResponseDataOrders order : orders) {
                        try {
                            logger.debug("开始处理订单: {}", order.getId());
                            List<StoreOrderInfo> orderInfos = CollUtil.newArrayList();
                            StoreOrder oldOrder = storeOrderService.getByOderId(order.getId());
                            Integer status = OrderUtil.orderStatusMapping(order.getStatus());

                            if (oldOrder != null) {
                                logger.debug("订单已存在，检查状态变化: orderId={}, oldStatus={}, newStatus={}",
                                        order.getId(), oldOrder.getStatus(), status);

                                //状态没有变，或者状态已经是完结状态
                                if (oldOrder.getStatus().equals(status) || oldOrder.getStatus() == 6) {
                                    logger.debug("订单状态无变化或已完结，跳过处理: orderId={}", order.getId());
                                    skippedCount++;
                                    continue;
                                }
                                logger.info("更新订单状态: orderId={}, oldStatus={}, newStatus={}",
                                        order.getId(), oldOrder.getStatus(), status);
                                // 更新订单详情中的实际返佣相关字段
                                updateOrderInfoActualCommission(order, oldOrder.getId());
                                //更新订单信息
                                storeOrder.setOrderId(order.getId());
                                storeOrder.setStatus(status);
                                storeOrder.setUpdateTime(new Date());
                                storeOrder.setId(oldOrder.getId());
                                storeOrderService.updateById(storeOrder);
                                nextByOrderStatus(oldOrder, status);
                                updatedCount++;
                                logger.debug("订单状态更新完成: orderId={}", order.getId());
                                continue;
                            }

                            logger.info("创建新订单: orderId={}, status={}", order.getId(), status);

                            storeOrder.setCreateTime(DateUtil.timeStamp11ToDate(order.getCreateTime() * 1000L));
                            storeOrder.setOutTradeNo(order.getId());
                            if (order.getDeliveryTime() != null) {
                                storeOrder.setDeliveryTime(DateUtil.timeStamp11ToDate(order.getDeliveryTime() * 1000L));
                            }
                            storeOrder.setStatus(status);
                            storeOrder.setType(2);
                            storeOrder.setOrderId(order.getId());

                            List<SearchCreatorAffiliateOrdersResponseDataOrdersSkus> skus = order.getSkus();
                            if (skus == null || CollUtil.isEmpty(skus)) {
                                logger.error("订单同步异常 - order中不存在sku: orderId={}", order.getId());
                                throw new CrmebException("订单同步-order中不存在sku..");
                            }

                            logger.debug("处理订单SKU信息: orderId={}, skuCount={}", order.getId(), skus.size());

                            BigDecimal totalPrice = new BigDecimal(0);
                            storeOrder.setTotalPrice(totalPrice);
                            for (SearchCreatorAffiliateOrdersResponseDataOrdersSkus sku : skus) {
                                orderInfos.add(generateStoreOrderInfo(order.getId(), sku, storeOrder));
                            }


                            storeOrder.setUid(OrderUtil.extraUidFromTagStr(skus.get(0).getTag()));
                            User user = userService.getById(storeOrder.getUid());
                            if (user == null) {
                                logger.error("订单同步异常，用户不存在：uid={}, orderNo={}",
                                        storeOrder.getUid(), storeOrder.getOutTradeNo());
                                errorCount++;
                                continue;
                            }

                            // 处理TikTok快速登录用户phone字段可能为空的情况
                            String userPhone = user.getPhone();
                            if (StrUtil.isBlank(userPhone)) {
                                userPhone = "-"; // 为空时使用默认值
                            }
                            storeOrder.setUserPhone(userPhone);
                            storeOrder.setRealName(user.getNickname());
                            storeOrder.setUserAddress("-");
                            storeOrder.setPayType("0");
                            storeOrder.setMark("-");
                            storeOrder.setCost(new BigDecimal("0"));

                            logger.debug("保存订单主表: orderId={}", order.getId());
                            storeOrderService.create(storeOrder);

                            logger.debug("保存订单详情: orderId={}, detailCount={}", order.getId(), orderInfos.size());
                            orderInfos.forEach(info -> info.setOrderId(storeOrder.getId()));
                            storeOrderInfoService.saveBatch(orderInfos);

                            processedCount++;
                            logger.info("订单处理完成: orderId={}, uid={}", order.getId(), storeOrder.getUid());

                        } catch (Exception orderException) {
                            logger.error("处理单个订单异常: orderId={}, error={}",
                                    order.getId(), orderException.getMessage(), orderException);
                            errorCount++;
                        }
                    }

                    logger.info("订单批量处理完成 - 总计: {}, 新增: {}, 更新: {}, 跳过: {}, 错误: {}",
                            orders.size(), processedCount, updatedCount, skippedCount, errorCount);
                    return Boolean.TRUE;
                });

                logger.info("TikTok订单同步事务执行完成，下一页token: {}", nextPageToken);
            } else {
                logger.error("TikTok API调用失败，响应码: {}, 响应信息: {}",
                        resp != null ? resp.getCode() : "null",
                        resp != null ? resp.getMessage() : "null");
            }
        } catch (Exception e) {
            logger.error("TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: {}", e.getMessage(), e);
            throw new CrmebException("TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: " + e.getMessage());
        }

        logger.info("TikTok订单同步方法执行完成，返回下一页token: {}", nextPageToken);
        return nextPageToken;
    }

    @Override
    public String getPlatform() {
        return "tiktok";
    }

    @Override
    public String syncOrders(String pageToken, Date startTime, Date endTime) {
        return syncTiktokOrders(pageToken, startTime, endTime);
    }

    private StoreOrderInfo generateStoreOrderInfo(String orderNo, SearchCreatorAffiliateOrdersResponseDataOrdersSkus sku, StoreOrder storeOrder) {
        //查询一下本地商品
        StoreProduct storeProduct = storeProductService.getByOutProductId(sku.getProductId());
        StoreOrderInfo storeOrderInfo = new StoreOrderInfo();
        storeOrderInfo.setInfo("-");
        storeOrderInfo.setUnique(orderNo);
        storeOrderInfo.setSku("-");
        if (storeProduct != null) {
            storeOrderInfo.setImage(storeProduct.getImage());
        }
        storeOrderInfo.setWeight(new BigDecimal(0));
        storeOrderInfo.setVolume(new BigDecimal(0));
        storeOrderInfo.setGiveIntegral(0);
        storeOrderInfo.setVipPrice(new BigDecimal(0));
        storeOrderInfo.setOrderNo(orderNo);
        storeOrderInfo.setTag(sku.getTag());
        storeOrderInfo.setContentId(sku.getContentId());
        storeOrderInfo.setPayNum(sku.getQuantity());
        storeOrderInfo.setOutProductId(sku.getProductId());
        storeOrderInfo.setCampaignId(sku.getCampaignId());
        storeOrderInfo.setProductName(sku.getProductName());
        storeOrderInfo.setProductType(5);
        storeOrderInfo.setShopName(sku.getShopName());
        storeOrderInfo.setRefundedQuantity(sku.getRefundedQuantity());
        storeOrderInfo.setReturnedQuantity(sku.getReturnedQuantity());
        if (sku.getPrice() != null && StringUtils.isNotEmpty(sku.getPrice().getAmount())) {
            storeOrderInfo.setPrice(new BigDecimal(StringUtils.substring(sku.getPrice().getAmount(), 2).replace(".", "")));
            storeOrder.setTotalPrice(storeOrder.getTotalPrice().add(storeOrderInfo.getPrice()));
        }
        if (sku.getCommissionRate() != null) {
            storeOrderInfo.setCommissionRate(new BigDecimal(sku.getCommissionRate()).multiply(new BigDecimal("0.0001")));
        }
        storeOrderInfo.setPayNum(sku.getQuantity());
        if (sku.getActualBonusCommission() != null && StringUtils.isNotEmpty(sku.getActualBonusCommission().getAmount())) {
            storeOrderInfo.setActualBonusCommission(new BigDecimal(StringUtils.substring(sku.getActualBonusCommission().getAmount(), 2).replace(".", "")));
        }
        if (sku.getActualCommission() != null && StringUtils.isNotEmpty(sku.getActualCommission().getAmount())) {
            storeOrderInfo.setActualCommission(new BigDecimal(StringUtils.substring(sku.getActualCommission().getAmount(), 2).replace(".", "")));
        }
        if (sku.getActualCommissionBase() != null && StringUtils.isNotEmpty(sku.getActualCommissionBase().getAmount())) {
            storeOrderInfo.setActualCommissionBase(new BigDecimal(StringUtils.substring(sku.getActualCommissionBase().getAmount(), 2).replace(".", "")));
        }
        if (sku.getActualCreatorCommissionRewardFee() != null && StringUtils.isNotEmpty(sku.getActualCreatorCommissionRewardFee().getAmount())) {
            storeOrderInfo.setActualCreatorCommissionRewardFee(new BigDecimal(StringUtils.substring(sku.getActualCreatorCommissionRewardFee().getAmount(), 2).replace(".", "")));
        }
        if (sku.getCommissionBonusRate() != null) {
            storeOrderInfo.setCommissionBonusRate(new BigDecimal(sku.getCommissionBonusRate()));
        }
        if (sku.getEstimatedBonusCommission() != null && StringUtils.isNotEmpty(sku.getEstimatedBonusCommission().getAmount())) {
            storeOrderInfo.setEstimatedBonusCommission(new BigDecimal(StringUtils.substring(sku.getEstimatedBonusCommission().getAmount(), 2).replace(".", "")));
        }
        if (sku.getEstimatedCommissionBase() != null && StringUtils.isNotEmpty(sku.getEstimatedCommissionBase().getAmount())) {
            storeOrderInfo.setEstimatedCommissionBase(new BigDecimal(StringUtils.substring(sku.getEstimatedCommissionBase().getAmount(), 2).replace(".", "")));
        }
        if (sku.getEstimatedCommission() != null && StringUtils.isNotEmpty(sku.getEstimatedCommission().getAmount())) {
            storeOrderInfo.setEstimatedCommission(new BigDecimal(StringUtils.substring(sku.getEstimatedCommission().getAmount(), 2).replace(".", "")));
        }
        //如果预计返现金额没有返回，则根据返现率和预估返现基数计算一把
        if (sku.getEstimatedCommission() == null && sku.getCommissionRate() != null
                && sku.getEstimatedCommissionBase() != null) {
            storeOrderInfo.setEstimatedCommission(storeOrderInfo.getCommissionRate()
                    .multiply(storeOrderInfo.getEstimatedCommissionBase()));
        }
        if (sku.getEstimatedShopAdsCommission() != null && StringUtils.isNotEmpty(sku.getEstimatedShopAdsCommission().getAmount())) {
            storeOrderInfo.setEstimatedShopAdsCommission(new BigDecimal(StringUtils.substring(sku.getEstimatedShopAdsCommission().getAmount(), 2).replace(".", "")));
        }
        if (sku.getEstimatedCreatorCommissionRewardFee() != null && StringUtils.isNotEmpty(sku.getEstimatedCreatorCommissionRewardFee().getAmount())) {
            storeOrderInfo.setEstimatedCreatorCommissionRewardFee(new BigDecimal(StringUtils.substring(sku.getEstimatedCreatorCommissionRewardFee().getAmount(), 2).replace(".", "")));
        }
        if (sku.getShopAdsCommissionRate() != null) {
            storeOrderInfo.setShopAdsCommissionRate(sku.getShopAdsCommissionRate());
        }
        return storeOrderInfo;
    }

    /**
     * 更新订单详情中的实际返佣相关字段
     *
     * @param order   TikTok订单数据
     * @param orderId 本地订单ID
     */
    private void updateOrderInfoActualCommission(SearchCreatorAffiliateOrdersResponseDataOrders order, Integer orderId) {
        try {
            logger.debug("开始更新订单详情的实际返佣字段: orderId={}", orderId);

            // 获取订单详情列表
            List<StoreOrderInfo> orderInfos = storeOrderInfoService.getListByOrderNo(order.getId());
            if (CollUtil.isEmpty(orderInfos)) {
                logger.warn("未找到订单详情，跳过更新: orderId={}", orderId);
                return;
            }

            List<SearchCreatorAffiliateOrdersResponseDataOrdersSkus> skus = order.getSkus();
            if (CollUtil.isEmpty(skus)) {
                logger.warn("订单SKU数据为空，跳过更新: orderId={}", orderId);
                return;
            }

            // 创建SKU ID到SKU数据的映射
            Map<String, SearchCreatorAffiliateOrdersResponseDataOrdersSkus> skuMap = new HashMap<>();
            for (SearchCreatorAffiliateOrdersResponseDataOrdersSkus sku : skus) {
                skuMap.put(sku.getProductId(), sku);
            }

            // 更新每个订单详情的实际返佣字段
            for (StoreOrderInfo orderInfo : orderInfos) {
                SearchCreatorAffiliateOrdersResponseDataOrdersSkus sku = skuMap.get(orderInfo.getOutProductId());
                if (sku == null) {
                    logger.warn("未找到对应的SKU数据: orderId={}, productId={}", orderId, orderInfo.getOutProductId());
                    continue;
                }

                // 更新实际返佣相关字段
                if (sku.getActualCommission() != null && StringUtils.isNotEmpty(sku.getActualCommission().getAmount())) {
                    orderInfo.setActualCommission(new BigDecimal(StringUtils.substring(sku.getActualCommission().getAmount(), 2).replace(".", "")));
                }
                if (sku.getActualCommissionBase() != null && StringUtils.isNotEmpty(sku.getActualCommissionBase().getAmount())) {
                    orderInfo.setActualCommissionBase(new BigDecimal(StringUtils.substring(sku.getActualCommissionBase().getAmount(), 2).replace(".", "")));
                }
                if (sku.getActualBonusCommission() != null && StringUtils.isNotEmpty(sku.getActualBonusCommission().getAmount())) {
                    orderInfo.setActualBonusCommission(new BigDecimal(StringUtils.substring(sku.getActualBonusCommission().getAmount(), 2).replace(".", "")));
                }
                if (sku.getActualCreatorCommissionRewardFee() != null && StringUtils.isNotEmpty(sku.getActualCreatorCommissionRewardFee().getAmount())) {
                    orderInfo.setActualCreatorCommissionRewardFee(new BigDecimal(StringUtils.substring(sku.getActualCreatorCommissionRewardFee().getAmount(), 2).replace(".", "")));
                }
                if (sku.getCommissionRate() != null) {
                    orderInfo.setCommissionRate(new BigDecimal(sku.getCommissionRate()).multiply(new BigDecimal("0.0001")));
                }

                // 更新数据库
                storeOrderInfoService.updateById(orderInfo);
                logger.debug("订单详情实际返佣字段更新完成: orderInfoId={}, productId={}", orderInfo.getId(), orderInfo.getOutProductId());
            }

            logger.info("订单详情实际返佣字段批量更新完成: orderId={}, detailCount={}", orderId, orderInfos.size());

        } catch (Exception e) {
            logger.error("更新订单详情实际返佣字段异常: orderId={}, error={}", orderId, e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 根据下一步状态，判断执行动作
     *
     * @param nextStatus The current status of the order. Possible options are:
     *                   - UNSPECIFIED: The status of the order is undefined. It might be updated later.
     *                   - ORDERED: The order has been placed, but the commission has not been settled. But an estimated commission is available.
     *                   - SETTLED: The commission of the order is already settled.
     *                   - REFUNDED: The order has been returned/refunded/canceled by the buyer, and no commission will be settled.
     *                   - FROZEN: Possible fraud has been detected regarding the order. The commission will be unfrozen after the fraud is resolved.
     *                   - DEDUCTED: Additional deduction from your balance account.
     * @param nextStatus
     * @return 返回状态值
     * <p>
     * 0：待发货；1：待收货；2：已收货，待评价；3：已完成
     * ——
     * 4：UNSPECIFIED
     * 5：ORDERED
     * 6：SETTLED
     * 7：REFUNDED
     * 8：FROZEN
     * 9：DEDUCTED
     */
    private void nextByOrderStatus(StoreOrder storeOrder, Integer nextStatus) {
        if (nextStatus == 4 || nextStatus == 5 || nextStatus == 8) {
            //状态未知，已下定进行中，冻结中

        } else if (nextStatus == 6) {
            //完成结算了，需要结算（落地记录）
            userRewardTaskService.createOrderRewardTask(storeOrder.getUid(), storeOrder.getOrderId(),
                    null, JSONUtil.toJsonStr(storeOrder));
        } else if (nextStatus == 7) {
            //退款了 无需结算

        } else if (nextStatus == 9) {
            //额外的账户处理 TODO（落地记录）
        }
    }
}